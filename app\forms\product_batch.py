"""
产品批量上架模块的表单
"""
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, FloatField, TextAreaField, SubmitField, HiddenField, SelectMultipleField, BooleanField
from wtforms.validators import DataRequired, Optional, Length, NumberRange
from wtforms.widgets import CheckboxInput, ListWidget

class MultiCheckboxField(SelectMultipleField):
    """多选复选框字段"""
    widget = ListWidget(prefix_label=False)
    option_widget = CheckboxInput()

class ProductBatchForm(FlaskForm):
    """产品批次表单"""
    name = StringField('批次名称', validators=[DataRequired(message='请输入批次名称'), Length(max=100)], default='')
    category_id = SelectField('食材分类', coerce=int, validators=[DataRequired(message='请选择食材分类')])
    supplier_id = SelectField('供应商', coerce=int, validators=[DataRequired(message='请选择供应商')])
    submit = SubmitField('下一步')

class ProductBatchIngredientForm(FlaskForm):
    """产品批次食材选择表单"""
    batch_id = HiddenField('批次ID', validators=[DataRequired()])
    ingredients = MultiCheckboxField('选择食材', coerce=int, validators=[DataRequired(message='请至少选择一个食材')])
    submit = SubmitField('下一步')

class ProductBatchAttributeForm(FlaskForm):
    """产品批次通用属性表单"""
    batch_id = HiddenField('批次ID', validators=[DataRequired()])
    price_strategy = SelectField('价格策略', choices=[
        ('fixed', '固定价格（所有产品使用相同价格）'),
        ('category', '按食材类别设置不同价格'),
        ('individual', '单独设置每个产品的价格')
    ], default='fixed')
    fixed_price = FloatField('固定价格', validators=[Optional(), NumberRange(min=0)], default=0.0)
    quality_cert = StringField('质量认证', validators=[DataRequired(message='请输入质量认证'), Length(min=2, max=200)], default='国家标准')
    quality_standard = StringField('质量标准', validators=[Optional(), Length(max=200)], default='GB/T')
    lead_time = StringField('供货周期(天)', validators=[Optional(), Length(max=20)], default='3')
    min_order_quantity = FloatField('最小订购量', validators=[Optional(), NumberRange(min=0)], default=1.0)
    default_unit_id = SelectField('默认规格单位', coerce=int, validators=[DataRequired(message='请选择默认规格单位')])
    submit = SubmitField('下一步')

class ProductBatchAdjustForm(FlaskForm):
    """产品批次个性化调整表单"""
    batch_id = HiddenField('批次ID', validators=[DataRequired()])
    submit = SubmitField('提交')

class ProductBatchConfirmForm(FlaskForm):
    """产品批次确认表单"""
    batch_id = HiddenField('批次ID', validators=[DataRequired()])
    submit = SubmitField('确认添加')

class ProductBatchApproveForm(FlaskForm):
    """产品批次审核表单"""
    batch_id = HiddenField('批次ID', validators=[DataRequired()])
    approve_all = BooleanField('全部审核通过', default=True)
    reject_reason = TextAreaField('拒绝原因', validators=[Optional(), Length(max=500)])
    submit = SubmitField('提交审核结果')
