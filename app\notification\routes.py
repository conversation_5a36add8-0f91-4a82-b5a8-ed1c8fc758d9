from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app import db
from app.notification import notification_bp
from app.models import Notification
from datetime import datetime

@notification_bp.route('/')
@login_required
def index():
    """通知中心页面"""
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # 获取筛选参数
    filter_type = request.args.get('type', 'all')

    # 基础查询
    query = Notification.query.filter_by(user_id=current_user.id)

    # 应用筛选
    if filter_type == 'unread':
        query = query.filter_by(is_read=0)
    elif filter_type != 'all':
        query = query.filter_by(notification_type=filter_type)

    # 分页
    pagination = query.order_by(Notification.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=0
    )
    notifications = pagination.items

    # 获取通知类型统计
    notification_types = db.session.query(
        Notification.notification_type,
        db.func.count(Notification.id).label('count')
    ).filter_by(user_id=current_user.id).group_by(Notification.notification_type).all()

    type_counts = {t[0]: t[1] for t in notification_types}
    unread_count = Notification.query.filter_by(user_id=current_user.id, is_read=0).count()

    return render_template(
        'notification/index.html',
        title='通知中心',
        notifications=notifications,
        pagination=pagination,
        filter_type=filter_type,
        type_counts=type_counts,
        unread_count=unread_count,
        now=datetime.now()
    )

@notification_bp.route('/view/<int:id>')
@login_required
def view(id):
    """查看单个通知"""
    notification = Notification.query.get_or_404(id)

    # 检查权限
    if notification.user_id != current_user.id:
        flash('您没有权限查看此通知', 'danger')
        return redirect(url_for('notification.index'))

    # 标记为已读
    if not notification.is_read:
        notification.is_read = 1
        db.session.commit()

    # 根据通知类型和引用类型处理跳转
    if notification.reference_id and notification.reference_type:
        if notification.reference_type == 'health_certificate':
            return redirect(url_for('employee.view_health_certificate', id=notification.reference_id))
        elif notification.reference_type == 'menu':
            return redirect(url_for('menu.view', id=notification.reference_id))
        # 其他类型的引用...

    # 默认返回通知详情页
    return render_template(
        'notification/view.html',
        title='查看通知',
        notification=notification,
        now=datetime.now()
    )

@notification_bp.route('/mark_read/<int:id>')
@login_required
def mark_read(id):
    """标记通知为已读"""
    notification = Notification.query.get_or_404(id)

    # 检查权限
    if notification.user_id != current_user.id:
        flash('您没有权限操作此通知', 'danger')
        return redirect(url_for('notification.index'))

    notification.is_read = 1
    db.session.commit()

    flash('通知已标记为已读', 'success')
    return redirect(url_for('notification.index'))

@notification_bp.route('/mark_all_read')
@login_required
def mark_all_read():
    """标记所有通知为已读"""
    Notification.query.filter_by(user_id=current_user.id, is_read=0).update({'is_read': 1})
    db.session.commit()

    flash('所有通知已标记为已读', 'success')
    return redirect(url_for('notification.index'))

@notification_bp.route('/delete/<int:id>', methods=['POST'])
@login_required
def delete(id):
    """删除通知"""
    notification = Notification.query.get_or_404(id)

    # 检查权限
    if notification.user_id != current_user.id:
        flash('您没有权限删除此通知', 'danger')
        return redirect(url_for('notification.index'))

    db.session.delete(notification)
    db.session.commit()

    flash('通知已删除', 'success')
    return redirect(url_for('notification.index'))

@notification_bp.route('/delete_all', methods=['POST'])
@login_required
def delete_all():
    """删除所有通知"""
    Notification.query.filter_by(user_id=current_user.id).delete()
    db.session.commit()

    flash('所有通知已删除', 'success')
    return redirect(url_for('notification.index'))

@notification_bp.route('/check')
@login_required
def check():
    """检查新通知（用于AJAX请求）"""
    unread_count = current_user.unread_notifications_count
    recent = current_user.recent_notifications

    notifications = []
    for n in recent:
        notifications.append({
            'id': n.id,
            'title': n.title,
            'content': n.content[:50] + '...' if len(n.content) > 50 else n.content,
            'is_read': n.is_read,
            'created_at': n.formatted_created_time,
            'level': n.level
        })

    return jsonify({
        'unread_count': unread_count,
        'notifications': notifications
    })

# 辅助函数
def get_notification_type_name(type_code):
    """获取通知类型名称"""
    type_names = {
        'system': '系统通知',
        'health_cert': '健康证提醒',
        'menu': '食谱通知',
        'purchase': '采购通知',
        'inspection': '检查通知',
        'task': '任务通知'
    }
    return type_names.get(type_code, type_code)
