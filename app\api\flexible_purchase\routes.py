from flask import jsonify, request, current_app
from flask_login import login_required, current_user
from app import db
from app.api.flexible_purchase import flexible_purchase_bp
from app.models import (
    MenuPlan, MenuRecipe, Recipe, RecipeIngredient, Ingredient, 
    AdministrativeArea, Supplier, SupplierSchoolRelation, SupplierProduct,
    Inventory, StockIn, PurchaseOrder, PurchaseOrderItem
)
from datetime import datetime, date, timedelta
import uuid

@flexible_purchase_bp.route('/reference', methods=['GET'])
@login_required
def get_purchase_reference():
    """获取日菜谱采购参考（按天食谱安排采购）"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]
    
    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    date_str = request.args.get('date')
    
    # 验证日期
    try:
        if date_str:
            target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        else:
            target_date = date.today()
    except ValueError:
        return jsonify({'success': False, 'message': '日期格式不正确'}), 400
    
    # 应用区域过滤
    if area_id:
        # 检查用户是否有权限查看该区域
        if area_id not in area_ids:
            return jsonify({'success': False, 'message': '您没有权限查看该区域的数据'}), 403
        area = AdministrativeArea.query.get(area_id)
        area_name = area.name if area else "未知区域"
    else:
        # 默认使用岳雅学校（示例）
        area = AdministrativeArea.query.filter_by(name='岳阳县岳雅学校').first()
        if area:
            area_id = area.id
            area_name = area.name
        else:
            # 如果找不到岳雅学校，则使用用户可访问的第一个区域
            if area_ids:
                area = AdministrativeArea.query.get(area_ids[0])
                area_id = area.id
                area_name = area.name if area else "未知区域"
            else:
                return jsonify({'success': False, 'message': '没有可访问的区域'}), 404
    
    # 获取指定日期的菜单计划
    menu_plans = MenuPlan.query.filter_by(
        area_id=area_id,
        plan_date=target_date
    ).all()
    
    # 构建菜单参考数据
    meals_data = {
        '早餐': None,
        '午餐': None,
        '晚餐': None
    }
    
    for plan in menu_plans:
        recipes_data = []
        
        for menu_recipe in plan.menu_recipes:
            recipe = menu_recipe.recipe
            if not recipe:
                continue
                
            # 获取菜品的参考食材
            reference_ingredients = []
            for recipe_ingredient in recipe.ingredients:
                ingredient = recipe_ingredient.ingredient
                if ingredient:
                    # 查找上次使用量（从历史记录中获取）
                    last_usage = recipe_ingredient.quantity  # 默认使用配方中的量
                    
                    # 可以在这里添加查询历史使用量的逻辑
                    
                    reference_ingredients.append({
                        'id': ingredient.id,
                        'name': ingredient.name,
                        'reference_quantity': recipe_ingredient.quantity,
                        'unit': recipe_ingredient.unit or ingredient.unit or '克',
                        'last_usage': last_usage
                    })
            
            recipes_data.append({
                'id': recipe.id,
                'name': recipe.name,
                'reference_ingredients': reference_ingredients
            })
        
        meals_data[plan.meal_type] = {
            'id': plan.id,
            'recipes': recipes_data
        }
    
    return jsonify({
        'success': True,
        'date': target_date.strftime('%Y-%m-%d'),
        'area_id': area_id,
        'area_name': area_name,
        'meals': meals_data
    })

@flexible_purchase_bp.route('/inventory/status', methods=['GET'])
@login_required
def get_inventory_status():
    """获取库存信息（包括保质期、批次、供应商）"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]
    
    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    ingredient_ids = request.args.get('ingredient_ids', '')
    
    # 应用区域过滤
    if area_id:
        # 检查用户是否有权限查看该区域
        if area_id not in area_ids:
            return jsonify({'success': False, 'message': '您没有权限查看该区域的数据'}), 403
        area = AdministrativeArea.query.get(area_id)
        area_name = area.name if area else "未知区域"
    else:
        # 默认使用岳雅学校（示例）
        area = AdministrativeArea.query.filter_by(name='岳阳县岳雅学校').first()
        if area:
            area_id = area.id
            area_name = area.name
        else:
            # 如果找不到岳雅学校，则使用用户可访问的第一个区域
            if area_ids:
                area = AdministrativeArea.query.get(area_ids[0])
                area_id = area.id
                area_name = area.name if area else "未知区域"
            else:
                return jsonify({'success': False, 'message': '没有可访问的区域'}), 404
    
    # 解析食材ID列表
    ingredient_id_list = []
    if ingredient_ids:
        try:
            ingredient_id_list = [int(id_str) for id_str in ingredient_ids.split(',') if id_str.strip()]
        except ValueError:
            return jsonify({'success': False, 'message': '食材ID格式不正确'}), 400
    
    # 查询库存
    inventory_query = Inventory.query.filter_by(area_id=area_id)
    if ingredient_id_list:
        inventory_query = inventory_query.filter(Inventory.ingredient_id.in_(ingredient_id_list))
    
    inventory_items = inventory_query.all()
    
    # 构建库存数据
    inventory_data = []
    for item in inventory_items:
        ingredient = item.ingredient
        if not ingredient:
            continue
        
        # 获取批次信息
        batches = []
        stock_ins = StockIn.query.filter_by(
            area_id=area_id,
            ingredient_id=ingredient.id
        ).order_by(StockIn.expiry_date).all()
        
        for stock_in in stock_ins:
            # 计算保质期剩余天数
            if stock_in.expiry_date:
                days_remaining = (stock_in.expiry_date - date.today()).days
            else:
                days_remaining = None
            
            supplier = stock_in.supplier
            
            batches.append({
                'batch_id': stock_in.batch_number or f"B{stock_in.id}",
                'quantity': stock_in.remaining_quantity,
                'expiry_date': stock_in.expiry_date.strftime('%Y-%m-%d') if stock_in.expiry_date else None,
                'days_remaining': days_remaining,
                'supplier': {
                    'id': supplier.id if supplier else None,
                    'name': supplier.name if supplier else '未知供应商'
                }
            })
        
        inventory_data.append({
            'ingredient_id': ingredient.id,
            'name': ingredient.name,
            'current_stock': item.quantity,
            'unit': item.unit or ingredient.unit or '克',
            'batches': batches
        })
    
    return jsonify({
        'success': True,
        'area_id': area_id,
        'area_name': area_name,
        'inventory': inventory_data
    })

@flexible_purchase_bp.route('/suppliers', methods=['GET'])
@login_required
def get_suppliers():
    """获取供应商列表（可按食材筛选）"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]
    
    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    ingredient_id = request.args.get('ingredient_id', type=int)
    
    # 应用区域过滤
    if area_id:
        # 检查用户是否有权限查看该区域
        if area_id not in area_ids:
            return jsonify({'success': False, 'message': '您没有权限查看该区域的数据'}), 403
    else:
        # 默认使用岳雅学校（示例）
        area = AdministrativeArea.query.filter_by(name='岳阳县岳雅学校').first()
        if area:
            area_id = area.id
        else:
            # 如果找不到岳雅学校，则使用用户可访问的第一个区域
            if area_ids:
                area_id = area_ids[0]
            else:
                return jsonify({'success': False, 'message': '没有可访问的区域'}), 404
    
    # 查询与该区域关联的供应商
    supplier_relations = SupplierSchoolRelation.query.filter_by(
        area_id=area_id,
        status=1  # 有效状态
    ).all()
    
    supplier_ids = [relation.supplier_id for relation in supplier_relations]
    suppliers = Supplier.query.filter(Supplier.id.in_(supplier_ids)).all()
    
    # 构建供应商数据
    suppliers_data = []
    for supplier in suppliers:
        # 获取供应商产品
        products = []
        supplier_products = SupplierProduct.query.filter_by(supplier_id=supplier.id).all()
        
        for product in supplier_products:
            ingredient = product.ingredient
            if ingredient:
                # 如果指定了食材ID，只返回匹配的产品
                if ingredient_id and ingredient.id != ingredient_id:
                    continue
                    
                products.append({
                    'ingredient_id': ingredient.id,
                    'name': ingredient.name,
                    'price': product.price,
                    'unit': product.unit or ingredient.unit or '克'
                })
        
        # 如果指定了食材ID且没有匹配的产品，跳过此供应商
        if ingredient_id and not products:
            continue
            
        # 获取供应商分类
        categories = list(set([product['name'].split()[0] for product in products if ' ' in product['name']]))
        
        # 计算评分（示例）
        rating = 4.5  # 这里可以根据实际情况计算评分
        
        # 获取最后一次送货日期（示例）
        last_delivery = "2025-05-01"  # 这里可以根据实际情况获取
        
        suppliers_data.append({
            'id': supplier.id,
            'name': supplier.name,
            'categories': categories,
            'rating': rating,
            'last_delivery': last_delivery,
            'products': products
        })
    
    return jsonify({
        'success': True,
        'suppliers': suppliers_data
    })

@flexible_purchase_bp.route('/plan', methods=['POST'])
@login_required
def create_purchase_plan():
    """创建采购计划（采购量由用户控制）"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]
    
    # 获取请求数据
    data = request.json
    if not data:
        return jsonify({'success': False, 'message': '请求数据不能为空'}), 400
    
    area_id = data.get('area_id')
    date_str = data.get('date')
    reference_menu_ids = data.get('reference_menu_ids', [])
    items = data.get('items', [])
    notes = data.get('notes', '')
    
    # 验证必要字段
    if not area_id or not date_str or not items:
        return jsonify({'success': False, 'message': '缺少必要字段'}), 400
    
    # 检查用户是否有权限操作该区域
    if area_id not in area_ids:
        return jsonify({'success': False, 'message': '您没有权限操作该区域'}), 403
    
    # 验证日期
    try:
        target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({'success': False, 'message': '日期格式不正确'}), 400
    
    # 创建采购订单
    order_number = f"PO{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6]}"
    purchase_order = PurchaseOrder(
        order_number=order_number,
        area_id=area_id,
        order_date=date.today(),
        delivery_date=target_date,
        status='待审核',
        created_by=current_user.id,
        notes=notes
    )
    
    # 添加参考菜单关联（如果有）
    if reference_menu_ids:
        reference_menus = MenuPlan.query.filter(
            MenuPlan.id.in_(reference_menu_ids),
            MenuPlan.area_id == area_id
        ).all()
        reference_menu_notes = ", ".join([f"{menu.meal_type}菜单" for menu in reference_menus])
        purchase_order.notes = f"{notes}\n参考菜单: {reference_menu_notes}"
    
    db.session.add(purchase_order)
    db.session.flush()  # 获取ID
    
    # 添加采购项目
    for item in items:
        ingredient_id = item.get('ingredient_id')
        quantity = item.get('quantity')
        unit = item.get('unit')
        supplier_id = item.get('supplier_id')
        
        if not ingredient_id or not quantity or not unit:
            continue
            
        # 验证食材是否存在
        ingredient = Ingredient.query.get(ingredient_id)
        if not ingredient:
            continue
            
        # 创建采购项目
        purchase_item = PurchaseOrderItem(
            order_id=purchase_order.id,
            ingredient_id=ingredient_id,
            quantity=quantity,
            unit=unit,
            supplier_id=supplier_id
        )
        
        db.session.add(purchase_item)
    
    # 提交事务
    try:
        db.session.commit()
        return jsonify({
            'success': True,
            'plan_id': purchase_order.id,
            'order_number': purchase_order.order_number,
            'message': '采购计划创建成功'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'创建采购计划失败: {str(e)}'}), 500
