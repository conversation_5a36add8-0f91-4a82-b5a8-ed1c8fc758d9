The sidebar used in this page to the left provides an example of what your sidebar would look like. Construction of a sidebar:

```html
<!--begin::Sidebar-->
<aside class="app-sidebar bg-body-secondary shadow" data-bs-theme="dark">
  <!--begin::Sidebar Brand-->
  <div class="sidebar-brand">
    <!--begin::Brand Link-->
    <a href="./index.html" class="brand-link">
      <!--begin::Brand Image-->
      <img
        src="./assets/img/AdminLTELogo.png"
        alt="AdminLTE Logo"
        class="brand-image opacity-75 shadow"
      />
      <!--end::Brand Image-->
      <!--begin::Brand Text-->
      <span class="brand-text fw-light">AdminLTE 4</span>
      <!--end::Brand Text-->
    </a>
    <!--end::Brand Link-->
  </div>
  <!--end::Sidebar Brand-->
  <!--begin::Sidebar Wrapper-->
  <div class="sidebar-wrapper">
    <nav class="mt-2">
      <!--begin::Sidebar Menu-->
      <ul
        class="nav sidebar-menu flex-column"
        data-lte-toggle="treeview"
        role="menu"
        data-accordion="false"
      >
        <li class="nav-header">Nav Header</li>
        <li class="nav-item">
          <a href="./index.html" class="nav-link">
            <i class="nav-icon bi bi-circle-fill"></i>
            <p>Level 1</p>
          </a>
        </li>
        <li class="nav-item">
          <a href="#" class="nav-link">
            <i class="nav-icon bi bi-circle-fill"></i>
            <p>
              Treeview
              <i class="nav-arrow bi bi-chevron-right"></i>
            </p>
          </a>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a href="./index.html" class="nav-link">
                <i class="nav-icon bi bi-circle"></i>
                <p>
                  Level 2 (Badge)
                  <span
                    class="nav-badge badge text-bg-secondary me-3"
                  >
                    6
                  </span>
                </p>
              </a>
            </li>
            <li class="nav-item">
              <a href="./index.html" class="nav-link">
                <i class="nav-icon bi bi-circle"></i>
                <p>Level 2</p>
              </a>
            </li>
          </ul>
        </li>
        <li class="nav-item">
          <a href="./index.html" class="nav-link active">
            <i class="nav-icon bi bi-circle-fill"></i>
            <p>Level 1 Active</p>
          </a>
        </li>

        <li class="nav-item menu-open">
          <a href="#" class="nav-link active">
            <i class="nav-icon bi bi-circle-fill"></i>
            <p>
              Treeview Menu Open
              <i class="nav-arrow bi bi-chevron-right"></i>
            </p>
          </a>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a href="./index.html" class="nav-link active">
                <i class="nav-icon bi bi-circle"></i>
                <p>Level 2 Active</p>
              </a>
            </li>
            <li class="nav-item">
              <a href="./index.html" class="nav-link">
                <i class="nav-icon bi bi-circle"></i>
                <p>Level 2</p>
              </a>
            </li>
          </ul>
        </li>
      </ul>
      <!--end::Sidebar Menu-->
    </nav>
  </div>
  <!--end::Sidebar Wrapper-->
</aside>
<!--end::Sidebar-->
```

## Alternate Logo

You can use two logo images instead of logo with text, you only need to change the markup to this:

```html
<div class="sidebar-brand">
  <a href="./index.html" class="brand-link logo-switch">
    <img
      src="./assets/img/AdminLTELogo.png"
      alt="AdminLTE Logo Small"
      class="brand-image-xl logo-xs opacity-75 shadow"
    />
    <img
      src="./assets/img/AdminLTEFullLogo.png"
      alt="AdminLTE Logo Large"
      class="brand-image-xs logo-xl opacity-75"
    />
  </a>
</div>
```
