"""
系统设置路由模块
"""

from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app import db
from app.admin import system_bp
from app.utils import admin_required, log_activity
from app.models_system import SystemSetting, SystemLog
from app.admin.system_settings import ensure_default_settings, get_settings_by_category
from datetime import datetime

@system_bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """系统管理仪表盘"""
    # 获取数据库统计信息
    from app.models import User, Supplier, Recipe, Ingredient

    # 简化的系统信息
    system_info = {
        'database': {
            'users': User.query.count(),
            'suppliers': Supplier.query.count() if 'Supplier' in globals() else 0,
            'recipes': Recipe.query.count() if 'Recipe' in globals() else 0,
            'ingredients': Ingredient.query.count() if 'Ingredient' in globals() else 0
        }
    }

    return render_template(
        'admin/system/dashboard.html',
        title='系统管理仪表盘',
        system_info=system_info,
        now=datetime.now()
    )

@system_bp.route('/settings')
@login_required
@admin_required
def settings():
    """系统设置页面"""
    # 确保默认设置存在
    ensure_default_settings()

    # 获取所有设置
    settings_by_category = get_settings_by_category()

    return render_template(
        'admin/system/settings.html',
        title='系统设置',
        settings=settings_by_category,
        now=datetime.now()
    )

@system_bp.route('/settings/update', methods=['POST'])
@login_required
@admin_required
def update_settings():
    """更新系统设置"""
    if not current_user.has_permission('setting', 'edit'):
        flash('您没有编辑系统设置的权限', 'danger')
        return redirect(url_for('system.settings'))

    try:
        # 获取表单数据
        form_data = request.form.to_dict()

        # 更新设置
        for key, value in form_data.items():
            if key.startswith('setting_'):
                setting_key = key.replace('setting_', '')
                value_type_key = f'value_type_{setting_key}'
                value_type = form_data.get(value_type_key, 'string')

                SystemSetting.set_value(
                    key=setting_key,
                    value=value,
                    value_type=value_type,
                    group='system',
                    is_public=False
                )

        flash('系统设置已更新', 'success')

        # 记录活动
        log_activity('update_settings', '更新系统设置', current_user.id)

        return redirect(url_for('system.settings'))
    except Exception as e:
        flash(f'更新系统设置失败: {str(e)}', 'danger')
        return redirect(url_for('system.settings'))

@system_bp.route('/backups')
@login_required
@admin_required
def backups():
    """数据库备份页面 - 功能已禁用"""
    flash('数据库备份功能暂时不可用', 'warning')
    return redirect(url_for('system.dashboard'))

@system_bp.route('/backups/create', methods=['POST'])
@login_required
@admin_required
def create_backup():
    """创建数据库备份 - 功能已禁用"""
    flash('数据库备份功能暂时不可用', 'warning')
    return redirect(url_for('system.dashboard'))

@system_bp.route('/backups/delete/<int:id>', methods=['POST'])
@login_required
@admin_required
def delete_backup(id):
    """删除数据库备份 - 功能已禁用"""
    flash('数据库备份功能暂时不可用', 'warning')
    return redirect(url_for('system.dashboard'))

@system_bp.route('/backups/download/<int:id>')
@login_required
@admin_required
def download_backup(id):
    """下载数据库备份 - 功能已禁用"""
    flash('数据库备份功能暂时不可用', 'warning')
    return redirect(url_for('system.dashboard'))

@system_bp.route('/logs')
@login_required
@admin_required
def logs():
    """系统日志页面 - 功能已禁用"""
    flash('系统日志功能暂时不可用', 'warning')
    return redirect(url_for('system.dashboard'))
