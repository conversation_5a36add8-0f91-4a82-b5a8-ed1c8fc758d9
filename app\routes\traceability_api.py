from flask import Blueprint, jsonify, request, current_app
from flask_login import login_required, current_user
from app.models import (
    Ingredient, Supplier, AdministrativeArea, User, AuditLog,
    StockIn, StockInItem, StockOut, StockOutItem, Inventory,
    ConsumptionPlan, ConsumptionDetail, MenuPlan, MenuRecipe, Recipe
)
from app.models_ingredient_traceability import MaterialBatch, TraceDocument, BatchFlow
from app import db
from datetime import datetime, date, timedelta
from sqlalchemy import func, text

traceability_api_bp = Blueprint('traceability_api', __name__)

@traceability_api_bp.route('/api/trace', methods=['GET'])
@login_required
def trace_food():
    """食材溯源接口"""
    # 获取参数
    trace_type = request.args.get('trace_type', '')
    trace_id = request.args.get('trace_id', type=int)
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    area_id = request.args.get('area_id', type=int)
    ingredient_id = request.args.get('ingredient_id', type=int)
    supplier_id = request.args.get('supplier_id', type=int)

    # 验证必要参数
    if not trace_type or not trace_id:
        return jsonify({'error': '缺少必要参数'}), 400

    # 根据溯源类型执行不同的查询逻辑
    if trace_type == 'menu_plan':
        return trace_from_menu_plan(trace_id, start_date, end_date, area_id, ingredient_id, supplier_id)
    elif trace_type == 'consumption_plan':
        return trace_from_consumption_plan(trace_id, start_date, end_date, area_id, ingredient_id, supplier_id)
    elif trace_type == 'stock_out':
        return trace_from_stock_out(trace_id, start_date, end_date, area_id, ingredient_id, supplier_id)
    elif trace_type == 'ingredient':
        return trace_from_ingredient(trace_id, start_date, end_date, area_id, supplier_id)
    else:
        return jsonify({'error': '不支持的溯源类型'}), 400

def trace_from_menu_plan(menu_plan_id, start_date=None, end_date=None, area_id=None, ingredient_id=None, supplier_id=None):
    """从菜单计划溯源"""
    # 1. 获取菜单计划信息
    menu_plan = MenuPlan.query.get_or_404(menu_plan_id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(menu_plan.area_id):
        return jsonify({'error': '您没有权限查看该菜单计划'}), 403

    # 2. 获取关联的消耗计划
    consumption_plans = ConsumptionPlan.query.filter_by(menu_plan_id=menu_plan_id).all()

    # 3. 获取消耗明细
    consumption_details = []
    for plan in consumption_plans:
        details = ConsumptionDetail.query.filter_by(consumption_plan_id=plan.id)
        if ingredient_id:
            details = details.filter_by(ingredient_id=ingredient_id)
        consumption_details.extend(details.all())

    # 4. 获取出库单和出库明细
    stock_outs = []
    stock_out_items = []
    for plan in consumption_plans:
        outs = StockOut.query.filter_by(consumption_plan_id=plan.id).all()
        stock_outs.extend(outs)
        for out in outs:
            items = StockOutItem.query.filter_by(stock_out_id=out.id)
            if ingredient_id:
                items = items.filter_by(ingredient_id=ingredient_id)
            stock_out_items.extend(items.all())

    # 5. 获取库存和入库信息
    inventory_data = []
    for item in stock_out_items:
        inventory = Inventory.query.get(item.inventory_id)
        if inventory:
            stock_in_items = StockInItem.query.filter_by(
                batch_number=inventory.batch_number,
                ingredient_id=inventory.ingredient_id
            ).all()

            for stock_in_item in stock_in_items:
                stock_in = StockIn.query.get(stock_in_item.stock_in_id)
                supplier = Supplier.query.get(stock_in_item.supplier_id)

                # 应用供应商过滤
                if supplier_id and supplier.id != supplier_id:
                    continue

                inventory_data.append({
                    'inventory_id': inventory.id,
                    'batch_number': inventory.batch_number,
                    'ingredient_id': inventory.ingredient_id,
                    'ingredient_name': inventory.ingredient.name if inventory.ingredient else None,
                    'quantity': item.quantity,
                    'unit': item.unit,
                    'stock_in_id': stock_in.id if stock_in else None,
                    'stock_in_number': stock_in.stock_in_number if stock_in else None,
                    'stock_in_date': stock_in.stock_in_date.strftime('%Y-%m-%d') if stock_in and stock_in.stock_in_date else None,
                    'supplier_id': supplier.id if supplier else None,
                    'supplier_name': supplier.name if supplier else None,
                    'production_date': stock_in_item.production_date.strftime('%Y-%m-%d') if stock_in_item.production_date else None,
                    'expiry_date': stock_in_item.expiry_date.strftime('%Y-%m-%d') if stock_in_item.expiry_date else None,
                    'quality_check_result': stock_in_item.quality_check_result,
                    'unit_price': float(stock_in_item.unit_price) if stock_in_item.unit_price else None
                })

    # 6. 构建溯源结果
    result = {
        'menu_plan': {
            'id': menu_plan.id,
            'plan_date': menu_plan.plan_date.strftime('%Y-%m-%d'),
            'meal_type': menu_plan.meal_type,
            'area_name': menu_plan.area.name if menu_plan.area else None,
            'status': menu_plan.status
        },
        'consumption_plans': [{
            'id': plan.id,
            'status': plan.status,
            'created_at': plan.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'creator': plan.creator.real_name or plan.creator.username if plan.creator else None
        } for plan in consumption_plans],
        'consumption_details': [{
            'id': detail.id,
            'ingredient_id': detail.ingredient_id,
            'ingredient_name': detail.ingredient.name if detail.ingredient else None,
            'planned_quantity': detail.planned_quantity,
            'actual_quantity': detail.actual_quantity,
            'unit': detail.unit,
            'status': detail.status
        } for detail in consumption_details],
        'stock_outs': [{
            'id': out.id,
            'stock_out_number': out.stock_out_number,
            'stock_out_date': out.stock_out_date.strftime('%Y-%m-%d %H:%M:%S') if out.stock_out_date else None,
            'stock_out_type': out.stock_out_type,
            'status': out.status,
            'operator': out.operator.real_name or out.operator.username if out.operator else None
        } for out in stock_outs],
        'stock_out_items': [{
            'id': item.id,
            'stock_out_id': item.stock_out_id,
            'ingredient_id': item.ingredient_id,
            'ingredient_name': item.ingredient.name if item.ingredient else None,
            'batch_number': item.batch_number,
            'quantity': item.quantity,
            'unit': item.unit
        } for item in stock_out_items],
        'inventory_data': inventory_data
    }

    return jsonify(result)

def trace_from_consumption_plan(consumption_plan_id, start_date=None, end_date=None, area_id=None, ingredient_id=None, supplier_id=None):
    """从消耗计划溯源"""
    # 1. 获取消耗计划信息
    consumption_plan = ConsumptionPlan.query.get_or_404(consumption_plan_id)

    # 2. 获取菜单计划
    menu_plan = MenuPlan.query.get(consumption_plan.menu_plan_id)

    # 检查用户是否有权限查看
    if menu_plan and not current_user.can_access_area_by_id(menu_plan.area_id):
        return jsonify({'error': '您没有权限查看该消耗计划'}), 403

    # 3. 获取消耗明细
    consumption_details = ConsumptionDetail.query.filter_by(consumption_plan_id=consumption_plan_id)
    if ingredient_id:
        consumption_details = consumption_details.filter_by(ingredient_id=ingredient_id)
    consumption_details = consumption_details.all()

    # 4. 获取出库单和出库明细
    stock_outs = StockOut.query.filter_by(consumption_plan_id=consumption_plan_id).all()
    stock_out_items = []
    for out in stock_outs:
        items = StockOutItem.query.filter_by(stock_out_id=out.id)
        if ingredient_id:
            items = items.filter_by(ingredient_id=ingredient_id)
        stock_out_items.extend(items.all())

    # 5. 获取库存和入库信息
    inventory_data = []
    for item in stock_out_items:
        inventory = Inventory.query.get(item.inventory_id)
        if inventory:
            stock_in_items = StockInItem.query.filter_by(
                batch_number=inventory.batch_number,
                ingredient_id=inventory.ingredient_id
            ).all()

            for stock_in_item in stock_in_items:
                stock_in = StockIn.query.get(stock_in_item.stock_in_id)
                supplier = Supplier.query.get(stock_in_item.supplier_id)

                # 应用供应商过滤
                if supplier_id and supplier.id != supplier_id:
                    continue

                inventory_data.append({
                    'inventory_id': inventory.id,
                    'batch_number': inventory.batch_number,
                    'ingredient_id': inventory.ingredient_id,
                    'ingredient_name': inventory.ingredient.name if inventory.ingredient else None,
                    'quantity': item.quantity,
                    'unit': item.unit,
                    'stock_in_id': stock_in.id if stock_in else None,
                    'stock_in_number': stock_in.stock_in_number if stock_in else None,
                    'stock_in_date': stock_in.stock_in_date.strftime('%Y-%m-%d') if stock_in and stock_in.stock_in_date else None,
                    'supplier_id': supplier.id if supplier else None,
                    'supplier_name': supplier.name if supplier else None,
                    'production_date': stock_in_item.production_date.strftime('%Y-%m-%d') if stock_in_item.production_date else None,
                    'expiry_date': stock_in_item.expiry_date.strftime('%Y-%m-%d') if stock_in_item.expiry_date else None,
                    'quality_check_result': stock_in_item.quality_check_result,
                    'unit_price': float(stock_in_item.unit_price) if stock_in_item.unit_price else None
                })

    # 6. 构建溯源结果
    result = {
        'consumption_plan': {
            'id': consumption_plan.id,
            'status': consumption_plan.status,
            'created_at': consumption_plan.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'creator': consumption_plan.creator.real_name or consumption_plan.creator.username if consumption_plan.creator else None
        },
        'menu_plan': {
            'id': menu_plan.id,
            'plan_date': menu_plan.plan_date.strftime('%Y-%m-%d'),
            'meal_type': menu_plan.meal_type,
            'area_name': menu_plan.area.name if menu_plan.area else None,
            'status': menu_plan.status
        } if menu_plan else None,
        'consumption_details': [{
            'id': detail.id,
            'ingredient_id': detail.ingredient_id,
            'ingredient_name': detail.ingredient.name if detail.ingredient else None,
            'planned_quantity': detail.planned_quantity,
            'actual_quantity': detail.actual_quantity,
            'unit': detail.unit,
            'status': detail.status
        } for detail in consumption_details],
        'stock_outs': [{
            'id': out.id,
            'stock_out_number': out.stock_out_number,
            'stock_out_date': out.stock_out_date.strftime('%Y-%m-%d %H:%M:%S') if out.stock_out_date else None,
            'stock_out_type': out.stock_out_type,
            'status': out.status,
            'operator': out.operator.real_name or out.operator.username if out.operator else None
        } for out in stock_outs],
        'stock_out_items': [{
            'id': item.id,
            'stock_out_id': item.stock_out_id,
            'ingredient_id': item.ingredient_id,
            'ingredient_name': item.ingredient.name if item.ingredient else None,
            'batch_number': item.batch_number,
            'quantity': item.quantity,
            'unit': item.unit
        } for item in stock_out_items],
        'inventory_data': inventory_data
    }

    return jsonify(result)

def trace_from_stock_out(stock_out_id, start_date=None, end_date=None, area_id=None, ingredient_id=None, supplier_id=None):
    """从出库单溯源"""
    # 1. 获取出库单信息
    stock_out = StockOut.query.get_or_404(stock_out_id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        return jsonify({'error': '您没有权限查看该出库单'}), 403

    # 2. 获取消耗计划
    consumption_plan = ConsumptionPlan.query.get(stock_out.consumption_plan_id) if stock_out.consumption_plan_id else None

    # 3. 获取菜单计划
    menu_plan = None
    if consumption_plan:
        menu_plan = MenuPlan.query.get(consumption_plan.menu_plan_id)

    # 4. 获取消耗明细
    consumption_details = []
    if consumption_plan:
        details = ConsumptionDetail.query.filter_by(consumption_plan_id=consumption_plan.id)
        if ingredient_id:
            details = details.filter_by(ingredient_id=ingredient_id)
        consumption_details = details.all()

    # 5. 获取出库明细
    stock_out_items = StockOutItem.query.filter_by(stock_out_id=stock_out_id)
    if ingredient_id:
        stock_out_items = stock_out_items.filter_by(ingredient_id=ingredient_id)
    stock_out_items = stock_out_items.all()

    # 6. 获取库存和入库信息
    inventory_data = []
    for item in stock_out_items:
        inventory = Inventory.query.get(item.inventory_id)
        if inventory:
            stock_in_items = StockInItem.query.filter_by(
                batch_number=inventory.batch_number,
                ingredient_id=inventory.ingredient_id
            ).all()

            for stock_in_item in stock_in_items:
                stock_in = StockIn.query.get(stock_in_item.stock_in_id)
                supplier = Supplier.query.get(stock_in_item.supplier_id)

                # 应用供应商过滤
                if supplier_id and supplier.id != supplier_id:
                    continue

                inventory_data.append({
                    'inventory_id': inventory.id,
                    'batch_number': inventory.batch_number,
                    'ingredient_id': inventory.ingredient_id,
                    'ingredient_name': inventory.ingredient.name if inventory.ingredient else None,
                    'quantity': item.quantity,
                    'unit': item.unit,
                    'stock_in_id': stock_in.id if stock_in else None,
                    'stock_in_number': stock_in.stock_in_number if stock_in else None,
                    'stock_in_date': stock_in.stock_in_date.strftime('%Y-%m-%d') if stock_in and stock_in.stock_in_date else None,
                    'supplier_id': supplier.id if supplier else None,
                    'supplier_name': supplier.name if supplier else None,
                    'production_date': stock_in_item.production_date.strftime('%Y-%m-%d') if stock_in_item.production_date else None,
                    'expiry_date': stock_in_item.expiry_date.strftime('%Y-%m-%d') if stock_in_item.expiry_date else None,
                    'quality_check_result': stock_in_item.quality_check_result,
                    'unit_price': float(stock_in_item.unit_price) if stock_in_item.unit_price else None
                })

    # 7. 构建溯源结果
    result = {
        'stock_out': {
            'id': stock_out.id,
            'stock_out_number': stock_out.stock_out_number,
            'stock_out_date': stock_out.stock_out_date.strftime('%Y-%m-%d %H:%M:%S') if stock_out.stock_out_date else None,
            'stock_out_type': stock_out.stock_out_type,
            'status': stock_out.status,
            'operator': stock_out.operator.real_name or stock_out.operator.username if stock_out.operator else None,
            'warehouse': stock_out.warehouse.name if stock_out.warehouse else None
        },
        'consumption_plan': {
            'id': consumption_plan.id,
            'status': consumption_plan.status,
            'created_at': consumption_plan.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'creator': consumption_plan.creator.real_name or consumption_plan.creator.username if consumption_plan.creator else None
        } if consumption_plan else None,
        'menu_plan': {
            'id': menu_plan.id,
            'plan_date': menu_plan.plan_date.strftime('%Y-%m-%d'),
            'meal_type': menu_plan.meal_type,
            'area_name': menu_plan.area.name if menu_plan.area else None,
            'status': menu_plan.status
        } if menu_plan else None,
        'consumption_details': [{
            'id': detail.id,
            'ingredient_id': detail.ingredient_id,
            'ingredient_name': detail.ingredient.name if detail.ingredient else None,
            'planned_quantity': detail.planned_quantity,
            'actual_quantity': detail.actual_quantity,
            'unit': detail.unit,
            'status': detail.status
        } for detail in consumption_details],
        'stock_out_items': [{
            'id': item.id,
            'ingredient_id': item.ingredient_id,
            'ingredient_name': item.ingredient.name if item.ingredient else None,
            'batch_number': item.batch_number,
            'quantity': item.quantity,
            'unit': item.unit
        } for item in stock_out_items],
        'inventory_data': inventory_data
    }

    return jsonify(result)

def trace_from_ingredient(ingredient_id, start_date=None, end_date=None, area_id=None, supplier_id=None):
    """从食材溯源"""
    # 1. 获取食材信息
    ingredient = Ingredient.query.get_or_404(ingredient_id)

    # 构建日期过滤条件
    date_filter = []
    if start_date:
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
        date_filter.append(StockIn.stock_in_date >= start_date_obj)
    if end_date:
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
        date_filter.append(StockIn.stock_in_date <= end_date_obj)

    # 2. 获取该食材的库存记录
    query = Inventory.query.filter_by(ingredient_id=ingredient_id)
    if area_id:
        query = query.join(Warehouse).filter(Warehouse.area_id == area_id)
    inventories = query.all()

    # 3. 获取入库记录
    stock_in_items_query = StockInItem.query.filter_by(ingredient_id=ingredient_id)
    if supplier_id:
        stock_in_items_query = stock_in_items_query.filter_by(supplier_id=supplier_id)
    stock_in_items = stock_in_items_query.all()

    # 4. 获取出库记录
    stock_out_items = StockOutItem.query.filter_by(ingredient_id=ingredient_id).all()

    # 5. 获取消耗计划
    consumption_details = ConsumptionDetail.query.filter_by(ingredient_id=ingredient_id).all()

    # 6. 构建溯源结果
    result = {
        'ingredient': {
            'id': ingredient.id,
            'name': ingredient.name,
            'category': ingredient.category.name if ingredient.category else None,
            'standard_unit': ingredient.standard_unit
        },
        'inventories': [{
            'id': inv.id,
            'batch_number': inv.batch_number,
            'warehouse_name': inv.warehouse.name if inv.warehouse else None,
            'quantity': inv.quantity,
            'unit': inv.unit,
            'status': inv.status,
            'production_date': inv.production_date.strftime('%Y-%m-%d') if inv.production_date else None,
            'expiry_date': inv.expiry_date.strftime('%Y-%m-%d') if inv.expiry_date else None
        } for inv in inventories],
        'stock_in_items': [{
            'id': item.id,
            'stock_in_id': item.stock_in_id,
            'stock_in_number': item.stock_in.stock_in_number if item.stock_in else None,
            'stock_in_date': item.stock_in.stock_in_date.strftime('%Y-%m-%d') if item.stock_in and item.stock_in.stock_in_date else None,
            'batch_number': item.batch_number,
            'quantity': item.quantity,
            'unit': item.unit,
            'supplier_id': item.supplier_id,
            'supplier_name': item.supplier.name if item.supplier else None,
            'production_date': item.production_date.strftime('%Y-%m-%d') if item.production_date else None,
            'expiry_date': item.expiry_date.strftime('%Y-%m-%d') if item.expiry_date else None,
            'quality_check_result': item.quality_check_result,
            'unit_price': float(item.unit_price) if item.unit_price else None
        } for item in stock_in_items],
        'stock_out_items': [{
            'id': item.id,
            'stock_out_id': item.stock_out_id,
            'stock_out_number': item.stock_out.stock_out_number if item.stock_out else None,
            'stock_out_date': item.stock_out.stock_out_date.strftime('%Y-%m-%d %H:%M:%S') if item.stock_out and item.stock_out.stock_out_date else None,
            'batch_number': item.batch_number,
            'quantity': item.quantity,
            'unit': item.unit,
            'consumption_plan_id': item.stock_out.consumption_plan_id if item.stock_out else None
        } for item in stock_out_items],
        'consumption_details': [{
            'id': detail.id,
            'consumption_plan_id': detail.consumption_plan_id,
            'planned_quantity': detail.planned_quantity,
            'actual_quantity': detail.actual_quantity,
            'unit': detail.unit,
            'status': detail.status,
            'menu_plan_id': detail.consumption_plan.menu_plan_id if detail.consumption_plan else None,
            'menu_plan_date': detail.consumption_plan.menu_plan.plan_date.strftime('%Y-%m-%d') if detail.consumption_plan and detail.consumption_plan.menu_plan else None,
            'menu_plan_meal_type': detail.consumption_plan.menu_plan.meal_type if detail.consumption_plan and detail.consumption_plan.menu_plan else None
        } for detail in consumption_details]
    }

    return jsonify(result)