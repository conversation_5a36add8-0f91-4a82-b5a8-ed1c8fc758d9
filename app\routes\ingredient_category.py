from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from flask_login import login_required, current_user
from app import db
from app.models import IngredientCategory, AuditLog
import json

ingredient_category_bp = Blueprint('ingredient_category', __name__)

@ingredient_category_bp.route('/')
@login_required
def index():
    """食材分类列表页面"""
    categories = IngredientCategory.query.all()
    # 构建分类树
    category_tree = []
    category_map = {category.id: category for category in categories}

    for category in categories:
        if category.parent_id is None:
            # 顶级分类
            category_data = category.to_dict()
            category_data['children'] = []
            category_tree.append(category_data)
        else:
            # 查找父分类
            for parent_category in category_tree:
                if parent_category['id'] == category.parent_id:
                    parent_category['children'].append(category.to_dict())
                    break

    return render_template('ingredient/categories.html', categories=categories, category_tree=json.dumps(category_tree))

@ingredient_category_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """创建食材分类"""
    if request.method == 'POST':
        name = request.form.get('name')
        parent_id = request.form.get('parent_id') or None
        description = request.form.get('description')

        if parent_id:
            parent_id = int(parent_id)

        category = IngredientCategory(
            name=name,
            parent_id=parent_id,
            description=description
        )

        db.session.add(category)

        # 添加审计日志
        log = AuditLog(
            user_id=current_user.id,
            action='create',
            resource_type='IngredientCategory',
            details=json.dumps(category.to_dict())
        )
        db.session.add(log)

        db.session.commit()
        flash('食材分类创建成功！', 'success')
        return redirect(url_for('ingredient_category.index'))

    # GET 请求，显示创建表单
    parent_categories = IngredientCategory.query.all()
    return render_template('ingredient/category_form.html', category=None, parent_categories=parent_categories)

@ingredient_category_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑食材分类"""
    category = IngredientCategory.query.get_or_404(id)

    if request.method == 'POST':
        name = request.form.get('name')
        parent_id = request.form.get('parent_id') or None
        description = request.form.get('description')

        if parent_id:
            parent_id = int(parent_id)
            # 检查是否选择了自己作为父分类
            if parent_id == id:
                flash('不能选择自己作为父分类！', 'danger')
                parent_categories = IngredientCategory.query.filter(IngredientCategory.id != id).all()
                return render_template('ingredient/category_form.html', category=category, parent_categories=parent_categories)

        old_data = category.to_dict()

        category.name = name
        category.parent_id = parent_id
        category.description = description

        # 添加审计日志
        log = AuditLog(
            user_id=current_user.id,
            action='update',
            resource_type='IngredientCategory',
            resource_id=category.id,
            details=json.dumps({
                'old': old_data,
                'new': category.to_dict()
            })
        )
        db.session.add(log)

        db.session.commit()
        flash('食材分类更新成功！', 'success')
        return redirect(url_for('ingredient_category.index'))

    # GET 请求，显示编辑表单
    parent_categories = IngredientCategory.query.filter(IngredientCategory.id != id).all()
    return render_template('ingredient/category_form.html', category=category, parent_categories=parent_categories)

@ingredient_category_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """删除食材分类"""
    category = IngredientCategory.query.get_or_404(id)

    # 检查是否有子分类
    if IngredientCategory.query.filter_by(parent_id=id).first():
        return jsonify({'success': 0, 'message': '该分类下有子分类，不能删除！'})

    # 检查是否有关联的食材
    if category.ingredients.count() > 0:
        return jsonify({'success': 0, 'message': '该分类下有关联的食材，不能删除！'})

    # 添加审计日志
    log = AuditLog(
        user_id=current_user.id,
        action='delete',
        resource_type='IngredientCategory',
        resource_id=category.id,
        details=json.dumps(category.to_dict())
    )
    db.session.add(log)

    db.session.delete(category)
    db.session.commit()

    return jsonify({'success': 1, 'message': '食材分类删除成功！'})

@ingredient_category_bp.route('/api')
@login_required
def api_list():
    """食材分类API"""
    categories = IngredientCategory.query.all()
    return jsonify([category.to_dict() for category in categories])
