from app import db
from datetime import datetime

class Notification(db.Model):
    """通知模型"""
    __tablename__ = 'notifications'

    id = db.<PERSON>umn(db.Integer, primary_key=1)
    user_id = db.Column(db.Integer, db.<PERSON><PERSON>('users.id'), nullable=0)
    title = db.Column(db.String(100), nullable=0)
    content = db.Column(db.Text, nullable=0)
    notification_type = db.Column(db.String(50), nullable=0)  # 通知类型
    level = db.Column(db.Integer, default=0)  # 通知级别: 0-普通, 1-重要, 2-紧急
    reference_id = db.Column(db.Integer)  # 引用ID，如健康证ID
    reference_type = db.Column(db.String(50))  # 引用类型
    is_read = db.Column(db.<PERSON>, default=0)  # 是否已读
    created_at = db.Column(DATETIME2(precision=1), default=lambda: datetime.now().replace(microsecond=0))

    @property
    def formatted_created_time(self):
        """格式化创建时间"""
        now = datetime.now()
        delta = now - self.created_at

        if delta.days == 0:
            if delta.seconds < 60:
                return "刚刚"
            elif delta.seconds < 3600:
                return f"{delta.seconds // 60}分钟前"
            else:
                return f"{delta.seconds // 3600}小时前"
        elif delta.days == 1:
            return "昨天"
        elif delta.days < 7:
            return f"{delta.days}天前"
        else:
            return self.created_at.strftime('%Y-%m-%d')

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'notification_type': self.notification_type,
            'level': self.level,
            'is_read': self.is_read,
            'created_at': self.formatted_created_time
        }
