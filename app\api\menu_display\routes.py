from flask import jsonify, request, current_app, url_for
from flask_login import login_required, current_user
from app import db
from app.api.menu_display import menu_display_bp
from app.models import MenuPlan, MenuRecipe, Recipe, AdministrativeArea
from datetime import datetime, date, timedelta
from app.utils.json_helper import json_response

@menu_display_bp.route('/week', methods=['GET'])
@login_required
def get_week_menu():
    """获取周菜谱数据（仅展示菜品，不关联食材采购）"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    week_start_str = request.args.get('week_start')

    # 如果没有指定周开始日期，默认为本周一
    if week_start_str:
        try:
            week_start = datetime.strptime(week_start_str, '%Y-%m-%d').date()
        except ValueError:
            # 如果日期格式不正确，使用当前日期
            today = date.today()
            week_start = today - timedelta(days=today.weekday())  # 本周一
    else:
        today = date.today()
        week_start = today - timedelta(days=today.weekday())  # 本周一

    # 计算周结束日期（周日）
    week_end = week_start + timedelta(days=6)

    # 构建查询
    query = MenuPlan.query.filter(
        MenuPlan.plan_date >= week_start,
        MenuPlan.plan_date <= week_end
    )

    # 应用区域过滤
    if area_id:
        # 检查用户是否有权限查看该区域
        if area_id not in area_ids:
            return jsonify({'success': False, 'message': '您没有权限查看该区域的菜单计划'}), 403
        query = query.filter_by(area_id=area_id)
        area = AdministrativeArea.query.get(area_id)
        area_name = area.name if area else "未知区域"
    else:
        # 默认使用岳雅学校（示例）
        area = AdministrativeArea.query.filter_by(name='岳阳县岳雅学校').first()
        if area:
            area_id = area.id
            area_name = area.name
            query = query.filter_by(area_id=area_id)
        else:
            # 如果找不到岳雅学校，则使用用户可访问的第一个区域
            if area_ids:
                query = query.filter(MenuPlan.area_id.in_(area_ids))
                area = AdministrativeArea.query.get(area_ids[0])
                area_name = area.name if area else "未知区域"
            else:
                return jsonify({'success': False, 'message': '没有可访问的区域'}), 404

    # 获取所有符合条件的菜单计划
    menu_plans = query.all()

    # 构建周视图数据结构
    week_data = []
    for i in range(7):
        current_date = week_start + timedelta(days=i)
        weekday = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][i]

        # 查找当天的菜单计划
        day_meals = {
            '早餐': None,
            '午餐': None,
            '晚餐': None
        }

        for plan in menu_plans:
            if plan.plan_date == current_date:
                # 获取菜单中的菜品，但不包含食材详情
                recipes = []
                for menu_recipe in plan.menu_recipes:
                    recipe = menu_recipe.recipe
                    if recipe:
                        recipes.append({
                            'id': recipe.id,
                            'name': recipe.name,
                            'image_url': recipe.main_image or url_for('static', filename='images/default_recipe.png'),
                            'description': recipe.description or '',
                            'tags': [recipe.category_rel.name if recipe.category_rel else '']
                        })

                day_meals[plan.meal_type] = {
                    'id': plan.id,
                    'status': plan.status,
                    'recipes': recipes
                }

        week_data.append({
            'date': current_date.strftime('%Y-%m-%d'),
            'weekday': weekday,
            'meals': day_meals
        })

    return json_response({
        'success': True,
        'week_start': week_start,  # 直接传递datetime对象，json_response会处理
        'week_end': week_end,      # 直接传递datetime对象，json_response会处理
        'area_id': area_id,
        'area_name': area_name,
        'week_data': week_data
    })

@menu_display_bp.route('/recipe/<int:recipe_id>', methods=['GET'])
@login_required
def get_recipe_details(recipe_id):
    """获取菜品详情（不包含食材用量）"""
    recipe = Recipe.query.get_or_404(recipe_id)

    # 构建菜品详情，但不包含食材用量
    recipe_data = {
        'id': recipe.id,
        'name': recipe.name,
        'category': recipe.category_rel.name if recipe.category_rel else '',
        'image_url': recipe.main_image or url_for('static', filename='images/default_recipe.png'),
        'description': recipe.description or '',
        'nutrition': {
            'calories': recipe.calories or 0
        },
        'cooking_method': recipe.cooking_method or '',
        'cooking_time': recipe.cooking_time or 0,
        'cooking_steps': recipe.cooking_steps or '',
        'tags': [recipe.category_rel.name if recipe.category_rel else '']
    }

    return json_response({
        'success': True,
        'recipe': recipe_data
    })

@menu_display_bp.route('/week/print', methods=['GET'])
@login_required
def print_week_menu():
    """获取适合打印的周菜谱数据"""
    # 复用get_week_menu的逻辑，但返回适合打印的格式
    response = get_week_menu()

    # 如果是错误响应，直接返回
    if response.status_code != 200:
        return response

    # 获取JSON数据
    data = response.get_json()

    # 构建打印友好的数据结构
    print_data = {
        'title': f"{data['area_name']}周菜谱 ({data['week_start']} 至 {data['week_end']})",
        'area_name': data['area_name'],
        'week_start': data['week_start'],
        'week_end': data['week_end'],
        'week_data': data['week_data']
    }

    return json_response({
        'success': True,
        'print_data': print_data
    })
