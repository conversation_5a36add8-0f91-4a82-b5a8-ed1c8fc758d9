//
// Component: Info Box
//

.info-box {
  @include box-shadow($lte-card-shadow);
  @include border-radius($border-radius);

  position: relative;
  display: flex;
  width: 100%;
  min-height: 80px;
  padding: .5rem;
  margin-bottom: map-get($spacers, 3);
  color: var(--#{$prefix}body-color);
  background-color: var(--#{$prefix}body-bg);

  .progress {
    height: 2px;
    margin: 5px 0;
    background-color: rgba(var(--#{$lte-prefix}card-variant-color-rgb), .125);

    .progress-bar {
      background-color: var(--#{$lte-prefix}card-variant-color);
    }
  }

  .info-box-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 70px;
    font-size: 1.875rem;
    text-align: center;
    @include border-radius($border-radius);

    > img {
      max-width: 100%;
    }
  }

  .info-box-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: center;
    padding: 0 10px;
    line-height: 1.8;
  }

  .info-box-number {
    display: block;
    margin-top: .25rem;
    font-weight: $font-weight-bold;
  }

  .progress-description,
  .info-box-text {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .info-box-more {
    display: block;
  }

  .progress-description {
    margin: 0;

  }

  @include media-breakpoint-up(md) {
    .col-xl-2 &,
    .col-lg-2 &,
    .col-md-2 & {
      .progress-description {
        display: none;
      }
    }

    .col-xl-3 &,
    .col-lg-3 &,
    .col-md-3 & {
      .progress-description {
        display: none;
      }
    }
  }

  @include media-breakpoint-up(lg) {
    .col-xl-2 &,
    .col-lg-2 &,
    .col-md-2 & {
      .progress-description {
        @include font-size(.75rem);
        display: block;
      }
    }

    .col-xl-3 &,
    .col-lg-3 &,
    .col-md-3 & {
      .progress-description {
        @include font-size(.75rem);
        display: block;
      }
    }
  }

  @include media-breakpoint-up(xl) {
    .col-xl-2 &,
    .col-lg-2 &,
    .col-md-2 & {
      .progress-description {
        @include font-size(1rem);
        display: block;
      }
    }

    .col-xl-3 &,
    .col-lg-3 &,
    .col-md-3 & {
      .progress-description {
        @include font-size(1rem);
        display: block;
      }
    }
  }
}
