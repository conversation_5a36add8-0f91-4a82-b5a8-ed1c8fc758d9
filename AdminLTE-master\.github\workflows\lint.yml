name: Lint

on:
  push:
    branches-ignore:
      - "dependabot/**"
  pull_request:
  workflow_dispatch:

env:
  FORCE_COLOR: 2
  NODE: 18

permissions:
  contents: read

jobs:
  run:
    runs-on: ubuntu-latest

    steps:
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "${{ env.NODE }}"
          cache: npm

      - name: Install npm dependencies
        run: npm ci

      - name: Run lint
        run: npm run lint